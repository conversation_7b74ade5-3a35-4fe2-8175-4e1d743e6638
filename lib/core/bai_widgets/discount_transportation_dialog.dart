import 'package:connectone/bai_blocs/mr_grouping/cubit/mr_grouping_cubit.dart';
import 'package:connectone/bai_models/discount_transport_res.dart';
import 'package:connectone/bai_models/offers_res.dart';
import 'package:connectone/bai_models/discount_tranpsortation_req.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../utils/tools.dart';
import '../../bai_models/discount_get_req.dart';

class DiscountTransportationDialog extends StatefulWidget {
  const DiscountTransportationDialog({
    Key? key,
    required this.orderGroupId,
    required this.sellerId,
    required this.categoryId,
    this.history,
    this.offerIds,
  }) : super(key: key);

  final int orderGroupId;
  final String sellerId;
  final NegotiationHistory? history;
  final int categoryId;
  final List<int>? offerIds;

  @override
  State<DiscountTransportationDialog> createState() =>
      _DiscountTransportationDialogState();
}

class _DiscountTransportationDialogState
    extends State<DiscountTransportationDialog> {
  final TextEditingController _discountPercentController =
      TextEditingController();
  final TextEditingController _discountAmountController =
      TextEditingController();
  final TextEditingController _transportationChargeController =
      TextEditingController();
  final TextEditingController _loadingChargeController =
      TextEditingController();
  DiscountTransportRes data = DiscountTransportRes();

  @override
  void initState() {
    super.initState();
    context.read<MrGroupingCubit>().getdiscountTransport(DiscountGetReq(
          ordrGrpId: widget.orderGroupId,
          sellerId: int.tryParse(widget.sellerId),
          categoryId: widget.categoryId,
          offerIds: widget.offerIds ?? [],
        ));
  }

  @override

  /// Builds a dialog to edit discount and transportation charges of a given order group.
  /// The dialog will fetch the current discount and transportation charges from the
  /// server on initialization, and update the server with the new values when the
  /// user submits the form.
  /// The dialog will also update the offer history with the new values.
  /// The dialog is a BlocConsumer widget, which means it will listen to the
  /// MrGroupingCubit for DiscountError and DiscountLoaded states.
  /// If the state is DiscountError, it will show an alert with the error message.
  /// If the state is DiscountLoaded, it will update the text fields with the new
  /// values.
  /// The dialog will also have a close button, which will pop the dialog off the
  /// navigator when tapped.
  /// The dialog will also have a submit button, which will post the new discount
  /// and transportation charges to the server and update the offer history when
  /// tapped.
  /// The dialog will have a loading indicator when the state is DiscountLoading.
  /// The dialog will also have a read-only field for the number of products and
  /// total price.
  Widget build(BuildContext context) {
    return BlocConsumer<MrGroupingCubit, MrGroupingState>(
      listener: (context, state) {
        if (state is DiscountError) {
          properAlert(state.message);
        }
        if (state is DiscountLoaded) {
          setState(() {
            data = state.discountTransportRes;
          });
          if (data.data != null) {
            if (data.data?.discount != null) {
              _discountAmountController.text =
                  data.data?.discount.toString() ?? '';
            }
            if (data.data?.discount != null && data.data?.totalPrice != null) {
              double discountAmount = data.data?.discount?.toDouble() ?? 1.0;
              double totalPrice = data.data!.totalPrice!.toDouble();
              double discountPercent = (discountAmount / totalPrice) * 100;
              _discountPercentController.text = discountAmount.toString();
              _discountPercentController.text =
                  discountPercent.toStringAsFixed(2);
            }
            if (data.data?.transportationCharges != null) {
              _transportationChargeController.text =
                  data.data?.transportationCharges.toString() ?? '';
            }
            if (data.data?.loading != null) {
              _loadingChargeController.text =
                  data.data?.loading.toString() ?? '';
            }
          }
        }
      },
      builder: (context, state) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          insetPadding: const EdgeInsets.all(8),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: state is DiscountLoading
                ? Container(
                    padding: const EdgeInsets.all(20),
                    height: 80,
                    width: 64,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(child: CircularProgressIndicator()),
                  )
                : SingleChildScrollView(
                    child: Column(
                      children: [
                        // Header Section
                        Container(
                          height: 52,
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppColors.primaryColorOld,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.discount,
                                color: Colors.white,
                                size: 16,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Discount & Transport Charges',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ).withCloseButton(() => Navigator.pop(context)),
                        const SizedBox(height: 24),

                        // Read-only fields for number of products and total price
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "Number of Products: ${data.data?.noOfProducts ?? 0}",
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "Total Price: ₹${data.data?.totalPrice?.toStringAsFixed(2) ?? '0.00'}",
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Transportation Charges field
                        const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Transportation Charges',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          style: const TextStyle(color: Colors.black),
                          controller: _transportationChargeController,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            hintText: '',
                            filled: false,
                            isDense: true,
                            fillColor: Colors.grey.shade300,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Loading Charges field
                        const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Loading Charges',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          style: const TextStyle(color: Colors.black),
                          controller: _loadingChargeController,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            hintText: '',
                            filled: false,
                            isDense: true,
                            fillColor: Colors.grey.shade300,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Discount % field
                        const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Discount %',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _discountPercentController,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            hintText: '',
                            filled: false,
                            isDense: true,
                            fillColor: Colors.white,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          onChanged: (value) {
                            _calculateDiscountFromPercent();
                          },
                        ),
                        const SizedBox(height: 16),

                        // Discount Amount field (read-only)
                        const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Discount Amount',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _discountAmountController,
                          keyboardType: TextInputType.number,
                          readOnly: true,
                          enabled: false,
                          style: const TextStyle(color: Colors.black),
                          decoration: InputDecoration(
                            hintText: '',
                            filled: true,
                            isDense: true,
                            fillColor: Colors.grey.shade200,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Submit and Close Buttons
                        SizedBox(
                          height: 40,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: BaiButton(
                                  onTap: () {
                                    Navigator.pop(context, false);
                                  },
                                  text: "CLOSE",
                                  backgoundColor: AppColors.red,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: BaiButton(
                                  onTap: () {
                                    double? discountAmount = double.tryParse(
                                        _discountAmountController.text);
                                    double? transportationCharge =
                                        double.tryParse(
                                            _transportationChargeController
                                                .text);
                                    double? loadingCharge = double.tryParse(
                                        _loadingChargeController.text);
                                    double? discountPercent = double.tryParse(
                                        _discountPercentController.text);

                                    // if (discountAmount == null) {
                                    //   alert("Please enter discount.");
                                    //   return;
                                    // }

                                    // if (transportationCharge == null) {
                                    //   alert(
                                    //       "Please enter transportation charges.");
                                    //   return;
                                    // }

                                    // if (loadingCharge == null) {
                                    //   alert("Please enter loading charges.");
                                    //   return;
                                    // }

                                    // if alteast one value is not null then proceed
                                    if (discountAmount == null &&
                                        transportationCharge == null &&
                                        loadingCharge == null) {
                                      alert("Please enter at least one value.");
                                      return;
                                    }

                                    context
                                        .read<MrGroupingCubit>()
                                        .postDiscount(DiscountReq(
                                          ordrGrpId: widget.orderGroupId,
                                          sellerId:
                                              int.tryParse(widget.sellerId),
                                          discount: discountAmount,
                                          transportationCharges:
                                              transportationCharge,
                                          loading: loadingCharge,
                                          discountPercent: discountPercent,
                                          categoryId: widget.categoryId,
                                          offerIds: widget.offerIds,
                                        ));
                                    Navigator.pop(context, true);
                                  },
                                  text: "SUBMIT",
                                  backgoundColor: AppColors.green,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
          ),
        );
      },
    );
  }

  void _calculateDiscountFromPercent() {
    if (_discountPercentController.text.isNotEmpty) {
      double? percent = double.tryParse(_discountPercentController.text);
      if (percent != null) {
        double totalPrice = data.data?.totalPrice?.toDouble() ?? 0.0;
        double discountAmount = totalPrice * (percent / 100);
        _discountAmountController.text = discountAmount.toStringAsFixed(2);
      }
    }
  }

  @override
  void dispose() {
    _discountPercentController.dispose();
    _discountAmountController.dispose();
    _transportationChargeController.dispose();
    _loadingChargeController.dispose();
    super.dispose();
  }
}
